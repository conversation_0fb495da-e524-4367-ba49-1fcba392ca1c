/**
 * Chrome Extension Source Viewer - Main Application
 * Coordinates all components and handles main application logic
 */

console.log('🚀 Loading main application...');

/**
 * Main Application class
 */
class App {
    constructor() {
        this.isInitialized = false;
        this.currentProject = null;
        
        console.log('🚀 App constructor called');
    }

    /**
     * Initialize the application
     */
    async init() {
        console.log('🚀 Initializing application...');
        
        try {
            // Wait for DOM to be ready
            if (document.readyState !== 'complete') {
                await new Promise(resolve => {
                    window.addEventListener('load', resolve);
                });
            }

            // Setup event listeners
            this.setupEventListeners();
            
            // Setup drag and drop
            this.setupDragAndDrop();
            
            // Initialize UI state
            this.initializeUI();
            
            this.isInitialized = true;
            console.log('✅ Application initialized successfully');
            
            Utils.updateStatus('التطبيق جاهز - اختر ملف إضافة أو حمل من المتجر');
            
        } catch (error) {
            console.error('❌ Error initializing application:', error);
            Utils.showError(`خطأ في تهيئة التطبيق: ${error.message}`);
        }
    }

    /**
     * Setup event listeners for main UI elements
     */
    setupEventListeners() {
        console.log('🔗 Setting up event listeners...');

        // Select file button
        const selectFileBtn = document.getElementById('selectFileBtn');
        if (selectFileBtn) {
            selectFileBtn.addEventListener('click', async (e) => {
                e.preventDefault();
                console.log('🖱️ Select file button clicked');
                await this.handleSelectFile();
            });
            console.log('✅ Select file button listener added');
        }

        // Download from store button
        const downloadBtn = document.getElementById('downloadBtn');
        if (downloadBtn) {
            downloadBtn.addEventListener('click', async (e) => {
                e.preventDefault();
                console.log('🖱️ Download button clicked');
                await this.handleDownloadFromStore();
            });
            console.log('✅ Download button listener added');
        }

        // Extension ID input - Enter key
        const extensionIdInput = document.getElementById('extensionIdInput');
        if (extensionIdInput) {
            extensionIdInput.addEventListener('keypress', async (e) => {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    console.log('⌨️ Enter pressed in extension ID input');
                    await this.handleDownloadFromStore();
                }
            });
            console.log('✅ Extension ID input listener added');
        }

        // New project button
        const newProjectBtn = document.getElementById('newProjectBtn');
        if (newProjectBtn) {
            newProjectBtn.addEventListener('click', (e) => {
                e.preventDefault();
                console.log('🖱️ New project button clicked');
                this.handleNewProject();
            });
            console.log('✅ New project button listener added');
        }

        // Save all button
        const saveAllBtn = document.getElementById('saveAllBtn');
        if (saveAllBtn) {
            saveAllBtn.addEventListener('click', async (e) => {
                e.preventDefault();
                console.log('🖱️ Save all button clicked');
                await this.handleSaveAll();
            });
            console.log('✅ Save all button listener added');
        }

        // Modal close buttons
        this.setupModalListeners();

        console.log('✅ Event listeners setup complete');
    }

    /**
     * Setup modal event listeners
     */
    setupModalListeners() {
        // Error modal close button
        const closeErrorBtn = document.getElementById('closeErrorBtn');
        if (closeErrorBtn) {
            closeErrorBtn.addEventListener('click', () => {
                Utils.hideError();
            });
        }

        // Success modal close button
        const closeSuccessBtn = document.getElementById('closeSuccessBtn');
        if (closeSuccessBtn) {
            closeSuccessBtn.addEventListener('click', () => {
                Utils.hideSuccess();
            });
        }

        // Close modals on background click
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('modal')) {
                if (e.target.id === 'errorModal') Utils.hideError();
                if (e.target.id === 'successModal') Utils.hideSuccess();
            }
        });
    }

    /**
     * Setup drag and drop functionality
     */
    setupDragAndDrop() {
        console.log('🎯 Setting up drag and drop...');

        const uploadArea = document.getElementById('uploadArea');
        if (!uploadArea) return;

        // Prevent default drag behaviors
        ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
            uploadArea.addEventListener(eventName, (e) => {
                e.preventDefault();
                e.stopPropagation();
            });
        });

        // Highlight drop area
        ['dragenter', 'dragover'].forEach(eventName => {
            uploadArea.addEventListener(eventName, () => {
                uploadArea.classList.add('drag-over');
            });
        });

        ['dragleave', 'drop'].forEach(eventName => {
            uploadArea.addEventListener(eventName, () => {
                uploadArea.classList.remove('drag-over');
            });
        });

        // Handle dropped files
        uploadArea.addEventListener('drop', async (e) => {
            console.log('📁 Files dropped');
            const files = Array.from(e.dataTransfer.files);
            
            if (files.length === 0) return;
            
            const file = files[0];
            const validExtensions = ['.crx', '.zip'];
            const fileExtension = file.name.toLowerCase().substring(file.name.lastIndexOf('.'));
            
            if (!validExtensions.includes(fileExtension)) {
                Utils.showError('يرجى اختيار ملف .crx أو .zip فقط');
                return;
            }
            
            console.log('📁 Valid file dropped:', file.name);
            Utils.showNotification(`تم إسقاط الملف: ${file.name}`, 'info');
            
            // Note: File processing would need to be implemented in the main process
            // For now, show a message that drag and drop is not fully implemented
            Utils.showError('وظيفة السحب والإفلات قيد التطوير. يرجى استخدام زر "اختيار ملف"');
        });

        console.log('✅ Drag and drop setup complete');
    }

    /**
     * Initialize UI state
     */
    initializeUI() {
        console.log('🎨 Initializing UI state...');

        // Show upload area initially
        const uploadArea = document.getElementById('uploadArea');
        const workArea = document.getElementById('workArea');
        
        if (uploadArea && workArea) {
            uploadArea.style.display = 'flex';
            workArea.style.display = 'none';
        }

        // Disable buttons that require a project
        const saveAllBtn = document.getElementById('saveAllBtn');
        if (saveAllBtn) saveAllBtn.disabled = true;

        console.log('✅ UI state initialized');
    }

    /**
     * Handle select file button click
     */
    async handleSelectFile() {
        console.log('📁 Handling file selection...');
        
        try {
            const result = await window.fileManager.selectFile();
            
            if (result && result.success) {
                console.log('✅ File selected successfully');
                await this.loadProject(result);
            }
            
        } catch (error) {
            console.error('❌ Error in file selection:', error);
        }
    }

    /**
     * Handle download from store button click
     */
    async handleDownloadFromStore() {
        console.log('🌐 Handling store download...');

        const extensionIdInput = document.getElementById('extensionIdInput');
        if (!extensionIdInput) {
            Utils.showError('حقل معرف الإضافة غير موجود');
            return;
        }

        const extensionId = extensionIdInput.value.trim();
        console.log('🔍 Extension ID entered:', extensionId);

        if (!extensionId) {
            Utils.showError('يرجى إدخال معرف الإضافة من متجر Chrome');
            extensionIdInput.focus();
            return;
        }

        // Client-side validation
        if (!/^[a-z]{32}$/.test(extensionId)) {
            Utils.showError('معرف الإضافة غير صحيح. يجب أن يكون 32 حرف صغير (a-z) بدون مسافات.\n\nمثال: cjpalhdlnbpafiamejdnhcphjbkeiagm');
            extensionIdInput.focus();
            extensionIdInput.select();
            return;
        }

        console.log('✅ Client-side validation passed');

        try {
            // Disable the button during download
            const downloadBtn = document.getElementById('downloadBtn');
            if (downloadBtn) {
                downloadBtn.disabled = true;
                downloadBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري التحميل...';
            }

            const result = await window.fileManager.downloadFromStore(extensionId);

            if (result && result.success) {
                console.log('✅ Extension downloaded successfully');
                await this.loadProject(result);

                // Clear input on success
                extensionIdInput.value = '';
                Utils.showNotification('تم تحميل الإضافة وعرضها بنجاح!', 'success');
            }

        } catch (error) {
            console.error('❌ Error in store download:', error);
            // Error is already handled in fileManager, just log here
        } finally {
            // Re-enable the button
            const downloadBtn = document.getElementById('downloadBtn');
            if (downloadBtn) {
                downloadBtn.disabled = false;
                downloadBtn.innerHTML = '<i class="fas fa-download"></i> تحميل';
            }
        }
    }

    /**
     * Handle new project button click
     */
    handleNewProject() {
        console.log('🆕 Handling new project...');
        
        if (window.fileManager.hasUnsavedChanges()) {
            const proceed = confirm('يوجد تغييرات غير محفوظة. هل تريد المتابعة؟');
            if (!proceed) return;
        }

        // Reset application state
        window.fileManager.newProject();
        window.fileTree.clearSelection();
        window.editor.showPlaceholder();
        window.preview.clear();
        
        this.currentProject = null;
        this.initializeUI();
        
        Utils.showNotification('تم إنشاء مشروع جديد', 'success');
    }

    /**
     * Handle save all button click
     */
    async handleSaveAll() {
        console.log('💾 Handling save all...');
        
        try {
            const success = await window.fileManager.saveAllFiles();
            if (success) {
                Utils.showNotification('تم حفظ جميع الملفات بنجاح', 'success');
            }
        } catch (error) {
            console.error('❌ Error saving all files:', error);
        }
    }

    /**
     * Load project into the application
     * @param {Object} projectData - Project data from file manager
     */
    async loadProject(projectData) {
        console.log('📂 Loading project:', projectData.project.name);
        
        try {
            this.currentProject = projectData.project;
            
            // Switch to work area
            this.switchToWorkArea();
            
            // Display file tree
            window.fileTree.display(projectData.files);
            
            // Update UI state
            this.updateUIForProject();
            
            // Show success notification
            Utils.showNotification(`تم تحميل المشروع: ${projectData.project.name}`, 'success');
            
            console.log('✅ Project loaded successfully');
            
        } catch (error) {
            console.error('❌ Error loading project:', error);
            Utils.showError(`خطأ في تحميل المشروع: ${error.message}`);
        }
    }

    /**
     * Switch from upload area to work area
     */
    switchToWorkArea() {
        console.log('🔄 Switching to work area...');
        
        const uploadArea = document.getElementById('uploadArea');
        const workArea = document.getElementById('workArea');
        
        if (uploadArea && workArea) {
            uploadArea.style.display = 'none';
            workArea.style.display = 'flex';
        }
    }

    /**
     * Update UI state for loaded project
     */
    updateUIForProject() {
        console.log('🎨 Updating UI for project...');
        
        // Enable project-related buttons
        const saveAllBtn = document.getElementById('saveAllBtn');
        if (saveAllBtn) {
            saveAllBtn.disabled = false;
        }
    }

    /**
     * Get current project
     * @returns {Object|null} Current project
     */
    getCurrentProject() {
        return this.currentProject;
    }

    /**
     * Check if application is initialized
     * @returns {boolean} True if initialized
     */
    isReady() {
        return this.isInitialized;
    }
}

// Create global app instance
const app = new App();
window.app = app;

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    console.log('📄 DOM Content Loaded - Initializing app...');
    
    // Small delay to ensure all scripts are loaded
    setTimeout(() => {
        app.init();
    }, 100);
});

// Handle window events
window.addEventListener('beforeunload', (e) => {
    if (window.fileManager && window.fileManager.hasUnsavedChanges()) {
        e.preventDefault();
        e.returnValue = 'يوجد تغييرات غير محفوظة. هل تريد المغادرة؟';
        return e.returnValue;
    }
});

// Add test function for debugging downloads
window.testDownload = async function(extensionId) {
    console.log('🧪 Testing download for extension ID:', extensionId);

    try {
        const result = await window.fileManager.downloadFromStore(extensionId);
        console.log('🧪 Test result:', result);

        if (result && result.success) {
            console.log('✅ Test download successful!');
            await app.loadProject(result);
        } else {
            console.error('❌ Test download failed:', result);
        }
    } catch (error) {
        console.error('❌ Test download error:', error);
    }
};

// Add some known working extension IDs for testing
window.testExtensions = {
    'adblock': 'cfhdojbkjhnklbpkdaibdccddilifddb',
    'honey': 'bfnaelmomeimhlpmgjnjophhpkkoljpa',
    'lastpass': 'hdokiejnpimakedhajhdlcegeplioahd',
    'grammarly': 'kbfnbcaeplbcioakkpcpgfkobkghlhen',
    'ublock': 'cjpalhdlnbpafiamejdnhcphjbkeiagm'
};

console.log('🧪 Test functions available:');
console.log('- testDownload("extensionId") - Test downloading a specific extension');
console.log('- testExtensions - Object with known extension IDs for testing');

console.log('✅ Main application script loaded successfully');
