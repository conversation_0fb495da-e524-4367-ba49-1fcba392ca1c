/**
 * Chrome Extension Source Viewer - File Manager
 * Handles file operations and project management
 */

console.log('📁 Loading file manager...');

/**
 * File Manager class for handling file operations
 */
class FileManager {
    constructor() {
        this.currentProject = null;
        this.openFiles = new Map();
        this.modifiedFiles = new Set();
        
        console.log('📁 FileManager initialized');
    }

    /**
     * Select and load a Chrome extension file
     * @returns {Promise<Object>} Result object with success status and data
     */
    async selectFile() {
        console.log('📁 Selecting file...');
        
        try {
            Utils.showLoading('جاري فتح نافذة اختيار الملف...');
            
            const result = await window.electronAPI.selectFile();
            
            Utils.hideLoading();
            
            if (result.success) {
                console.log('✅ File selected successfully:', result.project.name);
                this.currentProject = result.project;
                Utils.updateStatus(`تم تحميل المشروع: ${result.project.name}`);
                Utils.updateProjectInfo(result.project);
                return result;
            } else if (result.canceled) {
                console.log('ℹ️ File selection canceled');
                Utils.updateStatus('تم إلغاء اختيار الملف');
                return null;
            } else {
                throw new Error(result.error || 'فشل في اختيار الملف');
            }
            
        } catch (error) {
            console.error('❌ Error selecting file:', error);
            Utils.hideLoading();
            Utils.showError(`خطأ في اختيار الملف: ${error.message}`);
            Utils.updateStatus('خطأ في اختيار الملف');
            throw error;
        }
    }

    /**
     * Download extension from Chrome Web Store
     * @param {string} extensionId - Chrome extension ID
     * @returns {Promise<Object>} Result object with success status and data
     */
    async downloadFromStore(extensionId) {
        console.log('🌐 Downloading from store:', extensionId);

        try {
            // Clean and validate extension ID
            const cleanId = extensionId.trim().toLowerCase();
            console.log('🔍 Validating extension ID:', cleanId);

            if (!cleanId) {
                throw new Error('يرجى إدخال معرف الإضافة');
            }

            if (!/^[a-z]{32}$/.test(cleanId)) {
                throw new Error('معرف الإضافة غير صحيح. يجب أن يكون 32 حرف صغير (a-z) بدون مسافات أو رموز خاصة');
            }

            console.log('✅ Extension ID validation passed');

            Utils.showLoading('جاري تحميل الإضافة من متجر Chrome...');
            Utils.updateStatus('جاري الاتصال بمتجر Chrome...');

            console.log('📡 Sending download request to main process...');
            const result = await window.electronAPI.downloadFromStore(cleanId);

            console.log('📡 Download response received:', result);
            Utils.hideLoading();

            if (result.success) {
                console.log('✅ Extension downloaded successfully:', result.project.name);
                this.currentProject = result.project;
                Utils.updateStatus(`تم تحميل الإضافة: ${result.project.name}`);
                Utils.updateProjectInfo(result.project);
                Utils.showNotification(`تم تحميل الإضافة بنجاح: ${result.project.name}`, 'success');
                return result;
            } else {
                console.error('❌ Download failed:', result.error);
                throw new Error(result.error || 'فشل في تحميل الإضافة من المتجر');
            }

        } catch (error) {
            console.error('❌ Error downloading from store:', error);
            Utils.hideLoading();

            // Show user-friendly error message
            let userMessage = error.message;
            if (error.message.includes('ENOTFOUND')) {
                userMessage = 'فشل في الاتصال بمتجر Chrome. تحقق من اتصالك بالإنترنت وحاول مرة أخرى.';
            } else if (error.message.includes('timeout')) {
                userMessage = 'انتهت مهلة التحميل. الإضافة قد تكون كبيرة الحجم، حاول مرة أخرى.';
            } else if (error.message.includes('404')) {
                userMessage = 'الإضافة غير موجودة في المتجر. تأكد من صحة معرف الإضافة.';
            }

            Utils.showError(userMessage);
            Utils.updateStatus('فشل في التحميل من المتجر');
            throw error;
        }
    }

    /**
     * Read file content
     * @param {string} filePath - Relative file path
     * @returns {Promise<Object>} Result object with file content
     */
    async readFile(filePath) {
        console.log('📖 Reading file:', filePath);
        
        try {
            if (!this.currentProject) {
                throw new Error('لا يوجد مشروع محمل');
            }
            
            const result = await window.electronAPI.readFile(filePath, this.currentProject.extractPath);
            
            if (result.success) {
                console.log('✅ File read successfully:', filePath);
                
                // Cache the file content
                this.openFiles.set(filePath, {
                    content: result.content,
                    originalContent: result.content,
                    modified: false
                });
                
                return result;
            } else {
                throw new Error(result.error || 'فشل في قراءة الملف');
            }
            
        } catch (error) {
            console.error('❌ Error reading file:', error);
            Utils.showError(`خطأ في قراءة الملف: ${error.message}`);
            throw error;
        }
    }

    /**
     * Save file content
     * @param {string} filePath - Relative file path
     * @param {string} content - File content to save
     * @returns {Promise<Object>} Result object with success status
     */
    async saveFile(filePath, content) {
        console.log('💾 Saving file:', filePath);
        
        try {
            if (!this.currentProject) {
                throw new Error('لا يوجد مشروع محمل');
            }
            
            Utils.showLoading('جاري حفظ الملف...');
            
            const result = await window.electronAPI.saveFile(filePath, content, this.currentProject.extractPath);
            
            Utils.hideLoading();
            
            if (result.success) {
                console.log('✅ File saved successfully:', filePath);
                
                // Update cached content
                if (this.openFiles.has(filePath)) {
                    const fileData = this.openFiles.get(filePath);
                    fileData.content = content;
                    fileData.originalContent = content;
                    fileData.modified = false;
                    this.openFiles.set(filePath, fileData);
                }
                
                // Remove from modified files
                this.modifiedFiles.delete(filePath);
                
                Utils.showNotification('تم حفظ الملف بنجاح', 'success');
                Utils.updateStatus(`تم حفظ الملف: ${filePath.split('/').pop()}`);
                
                return result;
            } else {
                throw new Error(result.error || 'فشل في حفظ الملف');
            }
            
        } catch (error) {
            console.error('❌ Error saving file:', error);
            Utils.hideLoading();
            Utils.showError(`خطأ في حفظ الملف: ${error.message}`);
            throw error;
        }
    }

    /**
     * Update file content in memory (for editor changes)
     * @param {string} filePath - File path
     * @param {string} content - New content
     */
    updateFileContent(filePath, content) {
        if (this.openFiles.has(filePath)) {
            const fileData = this.openFiles.get(filePath);
            fileData.content = content;
            fileData.modified = content !== fileData.originalContent;
            
            if (fileData.modified) {
                this.modifiedFiles.add(filePath);
            } else {
                this.modifiedFiles.delete(filePath);
            }
            
            this.openFiles.set(filePath, fileData);
            
            // Update UI to show modified state
            this.updateFileTabState(filePath, fileData.modified);
        }
    }

    /**
     * Update file tab state in UI
     * @param {string} filePath - File path
     * @param {boolean} modified - Whether file is modified
     */
    updateFileTabState(filePath, modified) {
        const tabId = `tab-${filePath.replace(/[^a-zA-Z0-9]/g, '-')}`;
        const tab = document.getElementById(tabId);
        
        if (tab) {
            if (modified) {
                tab.classList.add('modified');
            } else {
                tab.classList.remove('modified');
            }
        }
    }

    /**
     * Save all modified files
     * @returns {Promise<boolean>} True if all files saved successfully
     */
    async saveAllFiles() {
        console.log('💾 Saving all modified files...');
        
        if (this.modifiedFiles.size === 0) {
            Utils.showNotification('لا توجد ملفات معدلة للحفظ', 'info');
            return true;
        }
        
        try {
            Utils.showLoading(`جاري حفظ ${this.modifiedFiles.size} ملف...`);
            
            const savePromises = Array.from(this.modifiedFiles).map(filePath => {
                const fileData = this.openFiles.get(filePath);
                if (fileData) {
                    return this.saveFile(filePath, fileData.content);
                }
            });
            
            await Promise.all(savePromises);
            
            Utils.hideLoading();
            Utils.showNotification('تم حفظ جميع الملفات بنجاح', 'success');
            Utils.updateStatus('تم حفظ جميع الملفات');
            
            return true;
            
        } catch (error) {
            console.error('❌ Error saving all files:', error);
            Utils.hideLoading();
            Utils.showError(`خطأ في حفظ الملفات: ${error.message}`);
            return false;
        }
    }

    /**
     * Close file (remove from open files)
     * @param {string} filePath - File path to close
     */
    closeFile(filePath) {
        console.log('🗂️ Closing file:', filePath);
        
        if (this.openFiles.has(filePath)) {
            const fileData = this.openFiles.get(filePath);
            
            if (fileData.modified) {
                // Ask user if they want to save changes
                const save = confirm(`الملف ${filePath.split('/').pop()} تم تعديله. هل تريد حفظ التغييرات؟`);
                if (save) {
                    this.saveFile(filePath, fileData.content);
                }
            }
            
            this.openFiles.delete(filePath);
            this.modifiedFiles.delete(filePath);
        }
    }

    /**
     * Create new project
     */
    newProject() {
        console.log('🆕 Creating new project...');
        
        if (this.hasUnsavedChanges()) {
            const proceed = confirm('يوجد تغييرات غير محفوظة. هل تريد المتابعة؟');
            if (!proceed) {
                return;
            }
        }
        
        this.currentProject = null;
        this.openFiles.clear();
        this.modifiedFiles.clear();
        
        Utils.updateStatus('جاهز');
        Utils.updateProjectInfo(null);
        Utils.updateFileInfo(null);
        
        // Reset UI
        this.resetUI();
    }

    /**
     * Check if there are unsaved changes
     * @returns {boolean} True if there are unsaved changes
     */
    hasUnsavedChanges() {
        return this.modifiedFiles.size > 0;
    }

    /**
     * Reset UI to initial state
     */
    resetUI() {
        // Show upload area, hide work area
        const uploadArea = document.getElementById('uploadArea');
        const workArea = document.getElementById('workArea');
        
        if (uploadArea && workArea) {
            uploadArea.style.display = 'flex';
            workArea.style.display = 'none';
        }
        
        // Clear file tree
        const fileTree = document.getElementById('fileTree');
        if (fileTree) {
            fileTree.innerHTML = '';
        }
        
        // Clear editor
        const editor = document.getElementById('editor');
        if (editor) {
            editor.innerHTML = `
                <div class="editor-placeholder">
                    <i class="fas fa-code"></i>
                    <p>اختر ملفاً من الشجرة لبدء التحرير</p>
                </div>
            `;
        }
        
        // Clear file tabs
        const fileTabs = document.getElementById('fileTabs');
        if (fileTabs) {
            fileTabs.innerHTML = '';
        }
    }

    /**
     * Get current project information
     * @returns {Object|null} Current project or null
     */
    getCurrentProject() {
        return this.currentProject;
    }

    /**
     * Get open files
     * @returns {Map} Map of open files
     */
    getOpenFiles() {
        return this.openFiles;
    }

    /**
     * Get modified files
     * @returns {Set} Set of modified file paths
     */
    getModifiedFiles() {
        return this.modifiedFiles;
    }
}

// Create global instance
const fileManager = new FileManager();
window.fileManager = fileManager;

console.log('✅ File manager loaded successfully');
