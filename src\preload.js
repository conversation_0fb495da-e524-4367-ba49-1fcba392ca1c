/**
 * Chrome Extension Source Viewer - Preload Script
 * Secure bridge between main and renderer processes
 */

const { contextBridge, ipcRenderer } = require('electron');

console.log('🔗 Preload script loading...');

// Expose secure API to renderer process
contextBridge.exposeInMainWorld('electronAPI', {
    // File operations
    selectFile: () => {
        console.log('🔗 Preload: selectFile called');
        return ipcRenderer.invoke('select-file');
    },
    
    downloadFromStore: (extensionId) => {
        console.log('🔗 Preload: downloadFromStore called with ID:', extensionId);
        return ipcRenderer.invoke('download-from-store', extensionId);
    },
    
    readFile: (filePath, projectPath) => {
        console.log('🔗 Preload: readFile called:', filePath);
        return ipcRenderer.invoke('read-file', filePath, projectPath);
    },
    
    saveFile: (filePath, content, projectPath) => {
        console.log('🔗 Preload: saveFile called:', filePath);
        return ipcRenderer.invoke('save-file', filePath, content, projectPath);
    },

    testStoreConnection: () => {
        console.log('🔗 Preload: testStoreConnection called');
        return ipcRenderer.invoke('test-store-connection');
    },
    
    // Utility functions
    getVersion: () => {
        return process.versions.electron;
    },
    
    getPlatform: () => {
        return process.platform;
    },
    
    // Event listeners for future use
    onMenuAction: (callback) => {
        ipcRenderer.on('menu-action', callback);
    },
    
    removeAllListeners: (channel) => {
        ipcRenderer.removeAllListeners(channel);
    }
});

// Security: Remove Node.js globals from renderer
delete window.require;
delete window.exports;
delete window.module;

console.log('✅ Preload script loaded successfully');
