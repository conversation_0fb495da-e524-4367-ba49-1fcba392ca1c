/**
 * Chrome Extension Source Viewer - Main Process
 * Complete rewrite with modern Electron architecture
 */

const { app, BrowserWindow, ipcMain, dialog, shell } = require('electron');
const path = require('path');
const fs = require('fs-extra');
const AdmZip = require('adm-zip');
const axios = require('axios');
const tmp = require('tmp');

// Keep a global reference of the window object
let mainWindow;

// Enable live reload for development
if (process.env.NODE_ENV === 'development') {
    require('electron-reload')(__dirname, {
        electron: path.join(__dirname, '..', 'node_modules', '.bin', 'electron'),
        hardResetMethod: 'exit'
    });
}

/**
 * Create the main application window
 */
function createMainWindow() {
    console.log('🚀 Creating main window...');
    
    mainWindow = new BrowserWindow({
        width: 1400,
        height: 900,
        minWidth: 1000,
        minHeight: 700,
        icon: path.join(__dirname, '..', 'assets', 'icon.png'),
        webPreferences: {
            nodeIntegration: false,
            contextIsolation: true,
            enableRemoteModule: false,
            preload: path.join(__dirname, 'preload.js'),
            webSecurity: true,
            allowRunningInsecureContent: false
        },
        titleBarStyle: 'default',
        show: false, // Don't show until ready
        backgroundColor: '#1e1e1e'
    });

    // Load the main HTML file
    const htmlPath = path.join(__dirname, 'renderer', 'index.html');
    console.log('📄 Loading HTML file:', htmlPath);
    
    mainWindow.loadFile(htmlPath);

    // Show window when ready to prevent visual flash
    mainWindow.once('ready-to-show', () => {
        console.log('✅ Window ready to show');
        mainWindow.show();
        
        // Open DevTools in development
        if (process.env.NODE_ENV === 'development') {
            mainWindow.webContents.openDevTools();
        }
    });

    // Handle window closed
    mainWindow.on('closed', () => {
        console.log('🔒 Main window closed');
        mainWindow = null;
    });

    // Handle external links
    mainWindow.webContents.setWindowOpenHandler(({ url }) => {
        shell.openExternal(url);
        return { action: 'deny' };
    });

    console.log('🏗️ Main window created successfully');
}

/**
 * App event handlers
 */
app.whenReady().then(() => {
    console.log('⚡ Electron app ready');
    createMainWindow();
    
    // macOS specific: recreate window when dock icon is clicked
    app.on('activate', () => {
        if (BrowserWindow.getAllWindows().length === 0) {
            createMainWindow();
        }
    });
});

// Quit when all windows are closed (except on macOS)
app.on('window-all-closed', () => {
    console.log('🚪 All windows closed');
    if (process.platform !== 'darwin') {
        app.quit();
    }
});

// Security: Prevent new window creation
app.on('web-contents-created', (event, contents) => {
    contents.on('new-window', (event, navigationUrl) => {
        event.preventDefault();
        shell.openExternal(navigationUrl);
    });
});

/**
 * IPC Handlers for file operations
 */

// Handle file selection
ipcMain.handle('select-file', async () => {
    console.log('📁 File selection requested');
    
    try {
        const result = await dialog.showOpenDialog(mainWindow, {
            title: 'اختر ملف إضافة Chrome',
            filters: [
                { name: 'Chrome Extensions', extensions: ['crx', 'zip'] },
                { name: 'CRX Files', extensions: ['crx'] },
                { name: 'ZIP Files', extensions: ['zip'] },
                { name: 'All Files', extensions: ['*'] }
            ],
            properties: ['openFile']
        });

        if (result.canceled || !result.filePaths.length) {
            console.log('📁 File selection canceled');
            return { success: false, canceled: true };
        }

        const filePath = result.filePaths[0];
        console.log('📁 File selected:', filePath);

        // Process the selected file
        return await processExtensionFile(filePath);

    } catch (error) {
        console.error('❌ Error in file selection:', error);
        return { 
            success: false, 
            error: `خطأ في اختيار الملف: ${error.message}` 
        };
    }
});

// Handle Chrome Web Store download
ipcMain.handle('download-from-store', async (event, extensionId) => {
    console.log('🌐 Store download requested for ID:', extensionId);

    try {
        // Validate extension ID
        if (!extensionId || !/^[a-z]{32}$/.test(extensionId)) {
            throw new Error('معرف الإضافة غير صحيح - يجب أن يكون 32 حرف صغير');
        }

        console.log('✅ Extension ID validation passed');

        // Create temporary file
        const tempFile = tmp.fileSync({ postfix: '.crx' });
        console.log('📦 Temporary file created:', tempFile.name);

        // Updated Chrome Web Store download URLs with better compatibility
        const chromeVersion = '120.0.6099.109';
        const downloadUrls = [
            // Primary URL - Most reliable format
            `https://clients2.google.com/service/update2/crx?response=redirect&prodversion=${chromeVersion}&acceptformat=crx2,crx3&x=id%3D${extensionId}%26uc`,
            // Alternative with more parameters
            `https://clients2.google.com/service/update2/crx?response=redirect&os=win&arch=x64&os_arch=x86_64&nacl_arch=x86-64&prod=chromiumcrx&prodchannel=stable&prodversion=${chromeVersion}&acceptformat=crx2,crx3&x=id%3D${extensionId}%26uc`,
            // Third-party CRX downloader (as backup)
            `https://crxextractor.com/download-extension.php?id=${extensionId}&format=crx`,
            // Direct Chrome Web Store (usually redirects)
            `https://chrome.google.com/webstore/detail/${extensionId}`
        ];

        let downloadUrl = downloadUrls[0]; // Start with primary URL

        // Try downloading from multiple URLs
        let response = null;
        let lastError = null;

        for (let i = 0; i < downloadUrls.length; i++) {
            downloadUrl = downloadUrls[i];
            console.log(`⬇️ Attempting download from URL ${i + 1}/${downloadUrls.length}:`, downloadUrl);

            try {
                // Make the download request with improved headers
                const requestConfig = {
                    method: 'GET',
                    url: downloadUrl,
                    responseType: 'stream',
                    timeout: 60000, // 60 seconds timeout
                    maxRedirects: 15, // Allow more redirects
                    headers: {
                        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8',
                        'Accept-Language': 'en-US,en;q=0.9',
                        'Accept-Encoding': 'gzip, deflate, br',
                        'Cache-Control': 'no-cache',
                        'Pragma': 'no-cache',
                        'Sec-Fetch-Dest': 'document',
                        'Sec-Fetch-Mode': 'navigate',
                        'Sec-Fetch-Site': 'none',
                        'Upgrade-Insecure-Requests': '1'
                    },
                    validateStatus: function (status) {
                        return status >= 200 && status < 400; // Accept redirects
                    }
                };

                // Add referer for Chrome Web Store URLs
                if (downloadUrl.includes('chrome.google.com') || downloadUrl.includes('clients2.google.com')) {
                    requestConfig.headers['Referer'] = 'https://chrome.google.com/webstore/';
                }

                console.log('📡 Making request with config:', {
                    url: downloadUrl,
                    headers: requestConfig.headers,
                    timeout: requestConfig.timeout
                });

                response = await axios(requestConfig);

                console.log(`✅ Successfully connected to URL ${i + 1}`);
                break; // Success, exit the loop

            } catch (error) {
                console.log(`❌ Failed to download from URL ${i + 1}:`, error.message);
                lastError = error;

                if (i === downloadUrls.length - 1) {
                    // This was the last URL, try alternative download method
                    console.log('🔄 All direct URLs failed, trying alternative download method...');
                    return await downloadExtensionAlternative(extensionId);
                }
                // Continue to next URL
            }
        }

        console.log('📊 Response status:', response.status);
        console.log('📊 Response headers:', response.headers);

        // Check if we got a valid response
        if (response.status !== 200) {
            throw new Error(`فشل في التحميل - رمز الحالة: ${response.status}`);
        }

        // Save the downloaded file
        const writer = fs.createWriteStream(tempFile.name);
        response.data.pipe(writer);

        await new Promise((resolve, reject) => {
            writer.on('finish', () => {
                console.log('✅ File write completed');
                resolve();
            });
            writer.on('error', (err) => {
                console.error('❌ File write error:', err);
                reject(err);
            });
        });

        // Check file size and content to ensure download was successful
        const stats = await fs.stat(tempFile.name);
        console.log('📊 Downloaded file size:', stats.size, 'bytes');

        if (stats.size < 100) {
            throw new Error('الملف المحمل صغير جداً - قد يكون هناك خطأ في التحميل');
        }

        // Read first few bytes to check if it's a valid CRX/ZIP file
        const buffer = await fs.readFile(tempFile.name);
        const firstBytes = buffer.slice(0, 4);

        // Check for ZIP signature (PK) or CRX signature
        const isZip = firstBytes[0] === 0x50 && firstBytes[1] === 0x4B; // PK
        const isCrx = firstBytes.toString() === 'Cr24'; // CRX signature

        console.log('📊 File signature check:', {
            firstBytes: Array.from(firstBytes).map(b => b.toString(16)).join(' '),
            isZip,
            isCrx
        });

        if (!isZip && !isCrx) {
            // The file might be an HTML error page, let's check
            const content = buffer.toString('utf8', 0, Math.min(500, buffer.length));
            console.log('📊 File content preview:', content.substring(0, 200));

            if (content.includes('<html') || content.includes('<!DOCTYPE')) {
                throw new Error('تم تحميل صفحة HTML بدلاً من الإضافة. قد يكون معرف الإضافة غير صحيح أو الإضافة غير متاحة للتحميل');
            } else {
                throw new Error('الملف المحمل ليس إضافة Chrome صحيحة');
            }
        }

        console.log('✅ Download completed successfully - valid extension file');

        // Process the downloaded file
        const result = await processExtensionFile(tempFile.name);

        // Clean up temporary file
        try {
            tempFile.removeCallback();
            console.log('🗑️ Temporary file cleaned up');
        } catch (cleanupError) {
            console.warn('⚠️ Failed to cleanup temporary file:', cleanupError);
        }

        return result;

    } catch (error) {
        console.error('❌ Error downloading from store:', error);

        // Provide more specific error messages
        let errorMessage = 'خطأ في التحميل من المتجر';

        if (error.code === 'ENOTFOUND') {
            errorMessage = 'فشل في الاتصال بمتجر Chrome - تحقق من الاتصال بالإنترنت';
        } else if (error.code === 'ETIMEDOUT') {
            errorMessage = 'انتهت مهلة التحميل - حاول مرة أخرى';
        } else if (error.response) {
            errorMessage = `خطأ من الخادم: ${error.response.status} - ${error.response.statusText}`;
        } else if (error.message.includes('معرف الإضافة')) {
            errorMessage = error.message;
        } else {
            errorMessage = `خطأ في التحميل: ${error.message}`;
        }

        return {
            success: false,
            error: errorMessage
        };
    }
});

/**
 * Alternative download method using third-party service
 */
async function downloadExtensionAlternative(extensionId) {
    console.log('🔄 Trying alternative download method for:', extensionId);

    try {
        // Create temporary file
        const tempFile = tmp.fileSync({ postfix: '.crx' });
        console.log('📦 Alternative temporary file created:', tempFile.name);

        // Use CRX Extractor service as fallback
        const alternativeUrl = `https://crxextractor.com/download-extension.php?id=${extensionId}&format=crx`;

        console.log('⬇️ Downloading from alternative service:', alternativeUrl);

        const response = await axios({
            method: 'GET',
            url: alternativeUrl,
            responseType: 'stream',
            timeout: 90000, // Longer timeout for third-party service
            headers: {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                'Accept': '*/*',
                'Referer': 'https://crxextractor.com/'
            }
        });

        console.log('📊 Alternative response status:', response.status);

        // Save the downloaded file
        const writer = fs.createWriteStream(tempFile.name);
        response.data.pipe(writer);

        await new Promise((resolve, reject) => {
            writer.on('finish', resolve);
            writer.on('error', reject);
        });

        // Check file validity
        const stats = await fs.stat(tempFile.name);
        console.log('📊 Alternative download file size:', stats.size, 'bytes');

        if (stats.size < 100) {
            throw new Error('الملف المحمل من الخدمة البديلة صغير جداً');
        }

        // Process the downloaded file
        const result = await processExtensionFile(tempFile.name);

        // Clean up
        try {
            tempFile.removeCallback();
        } catch (cleanupError) {
            console.warn('⚠️ Failed to cleanup alternative temp file:', cleanupError);
        }

        return result;

    } catch (error) {
        console.error('❌ Alternative download method failed:', error);
        throw new Error(`فشل في تحميل الإضافة من جميع المصادر المتاحة. تأكد من صحة معرف الإضافة وأن الإضافة متاحة للتحميل العام.`);
    }
}

/**
 * Process extension file (CRX or ZIP)
 */
async function processExtensionFile(filePath) {
    console.log('🔄 Processing extension file:', filePath);

    try {
        // First, validate the file before attempting extraction
        const fileBuffer = await fs.readFile(filePath);
        console.log('📊 File size:', fileBuffer.length, 'bytes');

        // Check file signature
        const signature = fileBuffer.slice(0, 4);
        const isZip = signature[0] === 0x50 && signature[1] === 0x4B; // "PK"
        const isCrx = signature.toString('ascii') === 'Cr24'; // CRX signature

        console.log('📊 File signature analysis:', {
            firstBytes: Array.from(signature).map(b => '0x' + b.toString(16).padStart(2, '0')).join(' '),
            isZip,
            isCrx,
            asText: signature.toString('ascii').replace(/[^\x20-\x7E]/g, '.')
        });

        // If it's neither ZIP nor CRX, check if it's an HTML error page
        if (!isZip && !isCrx) {
            const textContent = fileBuffer.toString('utf8', 0, Math.min(1000, fileBuffer.length));
            console.log('📄 File content preview (first 500 chars):', textContent.substring(0, 500));

            if (textContent.includes('<html') || textContent.includes('<!DOCTYPE') || textContent.includes('<HTML')) {
                // It's an HTML page - likely an error from Chrome Web Store
                if (textContent.includes('404') || textContent.includes('Not Found')) {
                    throw new Error('الإضافة غير موجودة في متجر Chrome. تأكد من صحة معرف الإضافة.');
                } else if (textContent.includes('403') || textContent.includes('Forbidden')) {
                    throw new Error('الوصول مرفوض لهذه الإضافة. قد تكون الإضافة محظورة أو غير متاحة للتحميل.');
                } else if (textContent.includes('error') || textContent.includes('Error')) {
                    throw new Error('خطأ من متجر Chrome. حاول مرة أخرى لاحقاً.');
                } else {
                    throw new Error('تم تحميل صفحة ويب بدلاً من الإضافة. قد يكون معرف الإضافة غير صحيح أو الإضافة غير متاحة للتحميل المباشر.');
                }
            } else if (textContent.includes('json') || textContent.startsWith('{')) {
                // Might be a JSON error response
                try {
                    const jsonResponse = JSON.parse(textContent);
                    throw new Error(`خطأ من متجر Chrome: ${jsonResponse.error || jsonResponse.message || 'استجابة غير متوقعة'}`);
                } catch (parseError) {
                    throw new Error('تم تحميل استجابة غير صحيحة من متجر Chrome. حاول مرة أخرى.');
                }
            } else {
                throw new Error('الملف المحمل ليس إضافة Chrome صحيحة. تأكد من صحة معرف الإضافة.');
            }
        }

        // Handle CRX files (Chrome extensions have a special header)
        let zipBuffer = fileBuffer;
        if (isCrx) {
            console.log('🧩 Processing CRX file...');
            // CRX files have a header before the ZIP data
            // CRX3 format: "Cr24" + version(4) + header_length(4) + header + ZIP
            const version = fileBuffer.readUInt32LE(4);
            console.log('🧩 CRX version:', version);

            if (version === 3) {
                // CRX3 format
                const headerLength = fileBuffer.readUInt32LE(8);
                const zipStart = 12 + headerLength;
                console.log('🧩 CRX3 header length:', headerLength, 'ZIP starts at:', zipStart);
                zipBuffer = fileBuffer.slice(zipStart);
            } else if (version === 2) {
                // CRX2 format: "Cr24" + version(4) + pub_key_length(4) + signature_length(4) + pub_key + signature + ZIP
                const pubKeyLength = fileBuffer.readUInt32LE(8);
                const signatureLength = fileBuffer.readUInt32LE(12);
                const zipStart = 16 + pubKeyLength + signatureLength;
                console.log('🧩 CRX2 pub_key_length:', pubKeyLength, 'signature_length:', signatureLength, 'ZIP starts at:', zipStart);
                zipBuffer = fileBuffer.slice(zipStart);
            } else {
                throw new Error(`إصدار CRX غير مدعوم: ${version}`);
            }

            // Verify the extracted ZIP portion has correct signature
            const zipSig = zipBuffer.slice(0, 2);
            if (!(zipSig[0] === 0x50 && zipSig[1] === 0x4B)) {
                throw new Error('ملف CRX تالف - لا يحتوي على بيانات ZIP صحيحة');
            }
        }

        // Create temporary extraction directory
        const extractDir = tmp.dirSync({ unsafeCleanup: true });
        console.log('📂 Extraction directory:', extractDir.name);

        // Extract the ZIP data
        console.log('📦 Extracting ZIP data...');
        const zip = new AdmZip(zipBuffer);
        zip.extractAllTo(extractDir.name, true);

        // Read manifest.json
        const manifestPath = path.join(extractDir.name, 'manifest.json');
        if (!await fs.pathExists(manifestPath)) {
            throw new Error('ملف manifest.json غير موجود');
        }

        const manifest = await fs.readJson(manifestPath);
        console.log('📋 Manifest loaded:', manifest.name);

        // Build file tree
        const fileTree = await buildFileTree(extractDir.name);

        return {
            success: true,
            project: {
                name: manifest.name || 'Unknown Extension',
                version: manifest.version || '1.0',
                description: manifest.description || '',
                extractPath: extractDir.name,
                manifest: manifest
            },
            files: fileTree
        };

    } catch (error) {
        console.error('❌ Error processing extension file:', error);
        return { 
            success: false, 
            error: `خطأ في معالجة الملف: ${error.message}` 
        };
    }
}

/**
 * Build file tree structure
 */
async function buildFileTree(dirPath, relativePath = '') {
    const items = [];
    
    try {
        const entries = await fs.readdir(dirPath, { withFileTypes: true });
        
        for (const entry of entries) {
            const fullPath = path.join(dirPath, entry.name);
            const relPath = path.join(relativePath, entry.name).replace(/\\/g, '/');
            
            if (entry.isDirectory()) {
                const children = await buildFileTree(fullPath, relPath);
                items.push({
                    name: entry.name,
                    path: relPath,
                    type: 'directory',
                    children: children
                });
            } else {
                const stats = await fs.stat(fullPath);
                items.push({
                    name: entry.name,
                    path: relPath,
                    type: 'file',
                    size: stats.size
                });
            }
        }
    } catch (error) {
        console.error('❌ Error building file tree:', error);
    }
    
    return items.sort((a, b) => {
        // Directories first, then files
        if (a.type !== b.type) {
            return a.type === 'directory' ? -1 : 1;
        }
        return a.name.localeCompare(b.name);
    });
}

// Handle file reading
ipcMain.handle('read-file', async (event, filePath, projectPath) => {
    console.log('📖 Reading file:', filePath);
    
    try {
        const fullPath = path.join(projectPath, filePath);
        
        if (!await fs.pathExists(fullPath)) {
            throw new Error('الملف غير موجود');
        }

        const content = await fs.readFile(fullPath, 'utf8');
        
        return {
            success: true,
            content: content
        };

    } catch (error) {
        console.error('❌ Error reading file:', error);
        return { 
            success: false, 
            error: `خطأ في قراءة الملف: ${error.message}` 
        };
    }
});

// Handle file saving
ipcMain.handle('save-file', async (event, filePath, content, projectPath) => {
    console.log('💾 Saving file:', filePath);
    
    try {
        const fullPath = path.join(projectPath, filePath);
        
        // Ensure directory exists
        await fs.ensureDir(path.dirname(fullPath));
        
        // Write file
        await fs.writeFile(fullPath, content, 'utf8');
        
        console.log('✅ File saved successfully');
        
        return { success: true };

    } catch (error) {
        console.error('❌ Error saving file:', error);
        return { 
            success: false, 
            error: `خطأ في حفظ الملف: ${error.message}` 
        };
    }
});

// Test Chrome Web Store download functionality
ipcMain.handle('test-store-connection', async () => {
    console.log('🧪 Testing Chrome Web Store connection...');

    try {
        const testUrl = 'https://clients2.google.com/service/update2/crx?response=redirect&prodversion=120.0.6099.109&acceptformat=crx2,crx3&x=id%3Dcjpalhdlnbpafiamejdnhcphjbkeiagm%26uc';

        const response = await axios({
            method: 'HEAD', // Just check if URL is accessible
            url: testUrl,
            timeout: 10000,
            headers: {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
            }
        });

        console.log('✅ Store connection test successful:', response.status);
        return { success: true, status: response.status };

    } catch (error) {
        console.error('❌ Store connection test failed:', error.message);
        return { success: false, error: error.message };
    }
});

console.log('🎉 Main process initialized successfully');
