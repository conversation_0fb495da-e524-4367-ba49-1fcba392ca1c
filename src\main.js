/**
 * Chrome Extension Source Viewer - Main Process
 * Complete rewrite with modern Electron architecture
 */

const { app, BrowserWindow, ipcMain, dialog, shell } = require('electron');
const path = require('path');
const fs = require('fs-extra');
const AdmZip = require('adm-zip');
const axios = require('axios');
const tmp = require('tmp');

// Keep a global reference of the window object
let mainWindow;

// Enable live reload for development
if (process.env.NODE_ENV === 'development') {
    require('electron-reload')(__dirname, {
        electron: path.join(__dirname, '..', 'node_modules', '.bin', 'electron'),
        hardResetMethod: 'exit'
    });
}

/**
 * Create the main application window
 */
function createMainWindow() {
    console.log('🚀 Creating main window...');
    
    mainWindow = new BrowserWindow({
        width: 1400,
        height: 900,
        minWidth: 1000,
        minHeight: 700,
        icon: path.join(__dirname, '..', 'assets', 'icon.png'),
        webPreferences: {
            nodeIntegration: false,
            contextIsolation: true,
            enableRemoteModule: false,
            preload: path.join(__dirname, 'preload.js'),
            webSecurity: true,
            allowRunningInsecureContent: false
        },
        titleBarStyle: 'default',
        show: false, // Don't show until ready
        backgroundColor: '#1e1e1e'
    });

    // Load the main HTML file
    const htmlPath = path.join(__dirname, 'renderer', 'index.html');
    console.log('📄 Loading HTML file:', htmlPath);
    
    mainWindow.loadFile(htmlPath);

    // Show window when ready to prevent visual flash
    mainWindow.once('ready-to-show', () => {
        console.log('✅ Window ready to show');
        mainWindow.show();
        
        // Open DevTools in development
        if (process.env.NODE_ENV === 'development') {
            mainWindow.webContents.openDevTools();
        }
    });

    // Handle window closed
    mainWindow.on('closed', () => {
        console.log('🔒 Main window closed');
        mainWindow = null;
    });

    // Handle external links
    mainWindow.webContents.setWindowOpenHandler(({ url }) => {
        shell.openExternal(url);
        return { action: 'deny' };
    });

    console.log('🏗️ Main window created successfully');
}

/**
 * App event handlers
 */
app.whenReady().then(() => {
    console.log('⚡ Electron app ready');
    createMainWindow();
    
    // macOS specific: recreate window when dock icon is clicked
    app.on('activate', () => {
        if (BrowserWindow.getAllWindows().length === 0) {
            createMainWindow();
        }
    });
});

// Quit when all windows are closed (except on macOS)
app.on('window-all-closed', () => {
    console.log('🚪 All windows closed');
    if (process.platform !== 'darwin') {
        app.quit();
    }
});

// Security: Prevent new window creation
app.on('web-contents-created', (event, contents) => {
    contents.on('new-window', (event, navigationUrl) => {
        event.preventDefault();
        shell.openExternal(navigationUrl);
    });
});

/**
 * IPC Handlers for file operations
 */

// Handle file selection
ipcMain.handle('select-file', async () => {
    console.log('📁 File selection requested');
    
    try {
        const result = await dialog.showOpenDialog(mainWindow, {
            title: 'اختر ملف إضافة Chrome',
            filters: [
                { name: 'Chrome Extensions', extensions: ['crx', 'zip'] },
                { name: 'CRX Files', extensions: ['crx'] },
                { name: 'ZIP Files', extensions: ['zip'] },
                { name: 'All Files', extensions: ['*'] }
            ],
            properties: ['openFile']
        });

        if (result.canceled || !result.filePaths.length) {
            console.log('📁 File selection canceled');
            return { success: false, canceled: true };
        }

        const filePath = result.filePaths[0];
        console.log('📁 File selected:', filePath);

        // Process the selected file
        return await processExtensionFile(filePath);

    } catch (error) {
        console.error('❌ Error in file selection:', error);
        return { 
            success: false, 
            error: `خطأ في اختيار الملف: ${error.message}` 
        };
    }
});

// Handle Chrome Web Store download
ipcMain.handle('download-from-store', async (event, extensionId) => {
    console.log('🌐 Store download requested for ID:', extensionId);

    try {
        // Validate extension ID
        if (!extensionId || !/^[a-z]{32}$/.test(extensionId)) {
            throw new Error('معرف الإضافة غير صحيح - يجب أن يكون 32 حرف صغير');
        }

        console.log('✅ Extension ID validation passed');

        // Create temporary file
        const tempFile = tmp.fileSync({ postfix: '.crx' });
        console.log('📦 Temporary file created:', tempFile.name);

        // Try multiple Chrome Web Store download URLs
        const chromeVersion = '120.0.6099.109';
        const downloadUrls = [
            // Primary URL - Google's official update service
            `https://clients2.google.com/service/update2/crx?response=redirect&prodversion=${chromeVersion}&acceptformat=crx2,crx3&x=id%3D${extensionId}%26uc`,
            // Alternative URL format
            `https://clients2.google.com/service/update2/crx?response=redirect&os=win&arch=x64&os_arch=x86_64&nacl_arch=x86-64&prod=chromiumcrx&prodchannel=&prodversion=${chromeVersion}&acceptformat=crx2,crx3&x=id%3D${extensionId}%26uc`,
            // Backup URL
            `https://chrome.google.com/webstore/download/${extensionId}?uc&authuser=0`
        ];

        let downloadUrl = downloadUrls[0]; // Start with primary URL

        // Try downloading from multiple URLs
        let response = null;
        let lastError = null;

        for (let i = 0; i < downloadUrls.length; i++) {
            downloadUrl = downloadUrls[i];
            console.log(`⬇️ Attempting download from URL ${i + 1}/${downloadUrls.length}:`, downloadUrl);

            try {
                // Make the download request with improved headers
                response = await axios({
                    method: 'GET',
                    url: downloadUrl,
                    responseType: 'stream',
                    timeout: 60000, // Increased timeout to 60 seconds
                    maxRedirects: 10, // Allow redirects
                    headers: {
                        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                        'Accept': '*/*',
                        'Accept-Language': 'en-US,en;q=0.9',
                        'Accept-Encoding': 'gzip, deflate, br',
                        'Connection': 'keep-alive',
                        'Upgrade-Insecure-Requests': '1',
                        'Referer': 'https://chrome.google.com/'
                    },
                    validateStatus: function (status) {
                        return status >= 200 && status < 400; // Accept redirects
                    }
                });

                console.log(`✅ Successfully connected to URL ${i + 1}`);
                break; // Success, exit the loop

            } catch (error) {
                console.log(`❌ Failed to download from URL ${i + 1}:`, error.message);
                lastError = error;

                if (i === downloadUrls.length - 1) {
                    // This was the last URL, throw the error
                    throw error;
                }
                // Continue to next URL
            }
        }

        console.log('📊 Response status:', response.status);
        console.log('📊 Response headers:', response.headers);

        // Check if we got a valid response
        if (response.status !== 200) {
            throw new Error(`فشل في التحميل - رمز الحالة: ${response.status}`);
        }

        // Save the downloaded file
        const writer = fs.createWriteStream(tempFile.name);
        response.data.pipe(writer);

        await new Promise((resolve, reject) => {
            writer.on('finish', () => {
                console.log('✅ File write completed');
                resolve();
            });
            writer.on('error', (err) => {
                console.error('❌ File write error:', err);
                reject(err);
            });
        });

        // Check file size and content to ensure download was successful
        const stats = await fs.stat(tempFile.name);
        console.log('📊 Downloaded file size:', stats.size, 'bytes');

        if (stats.size < 100) {
            throw new Error('الملف المحمل صغير جداً - قد يكون هناك خطأ في التحميل');
        }

        // Read first few bytes to check if it's a valid CRX/ZIP file
        const buffer = await fs.readFile(tempFile.name);
        const firstBytes = buffer.slice(0, 4);

        // Check for ZIP signature (PK) or CRX signature
        const isZip = firstBytes[0] === 0x50 && firstBytes[1] === 0x4B; // PK
        const isCrx = firstBytes.toString() === 'Cr24'; // CRX signature

        console.log('📊 File signature check:', {
            firstBytes: Array.from(firstBytes).map(b => b.toString(16)).join(' '),
            isZip,
            isCrx
        });

        if (!isZip && !isCrx) {
            // The file might be an HTML error page, let's check
            const content = buffer.toString('utf8', 0, Math.min(500, buffer.length));
            console.log('📊 File content preview:', content.substring(0, 200));

            if (content.includes('<html') || content.includes('<!DOCTYPE')) {
                throw new Error('تم تحميل صفحة HTML بدلاً من الإضافة. قد يكون معرف الإضافة غير صحيح أو الإضافة غير متاحة للتحميل');
            } else {
                throw new Error('الملف المحمل ليس إضافة Chrome صحيحة');
            }
        }

        console.log('✅ Download completed successfully - valid extension file');

        // Process the downloaded file
        const result = await processExtensionFile(tempFile.name);

        // Clean up temporary file
        try {
            tempFile.removeCallback();
            console.log('🗑️ Temporary file cleaned up');
        } catch (cleanupError) {
            console.warn('⚠️ Failed to cleanup temporary file:', cleanupError);
        }

        return result;

    } catch (error) {
        console.error('❌ Error downloading from store:', error);

        // Provide more specific error messages
        let errorMessage = 'خطأ في التحميل من المتجر';

        if (error.code === 'ENOTFOUND') {
            errorMessage = 'فشل في الاتصال بمتجر Chrome - تحقق من الاتصال بالإنترنت';
        } else if (error.code === 'ETIMEDOUT') {
            errorMessage = 'انتهت مهلة التحميل - حاول مرة أخرى';
        } else if (error.response) {
            errorMessage = `خطأ من الخادم: ${error.response.status} - ${error.response.statusText}`;
        } else if (error.message.includes('معرف الإضافة')) {
            errorMessage = error.message;
        } else {
            errorMessage = `خطأ في التحميل: ${error.message}`;
        }

        return {
            success: false,
            error: errorMessage
        };
    }
});

/**
 * Process extension file (CRX or ZIP)
 */
async function processExtensionFile(filePath) {
    console.log('🔄 Processing extension file:', filePath);
    
    try {
        // Create temporary extraction directory
        const extractDir = tmp.dirSync({ unsafeCleanup: true });
        console.log('📂 Extraction directory:', extractDir.name);

        // Extract the file
        const zip = new AdmZip(filePath);
        zip.extractAllTo(extractDir.name, true);

        // Read manifest.json
        const manifestPath = path.join(extractDir.name, 'manifest.json');
        if (!await fs.pathExists(manifestPath)) {
            throw new Error('ملف manifest.json غير موجود');
        }

        const manifest = await fs.readJson(manifestPath);
        console.log('📋 Manifest loaded:', manifest.name);

        // Build file tree
        const fileTree = await buildFileTree(extractDir.name);

        return {
            success: true,
            project: {
                name: manifest.name || 'Unknown Extension',
                version: manifest.version || '1.0',
                description: manifest.description || '',
                extractPath: extractDir.name,
                manifest: manifest
            },
            files: fileTree
        };

    } catch (error) {
        console.error('❌ Error processing extension file:', error);
        return { 
            success: false, 
            error: `خطأ في معالجة الملف: ${error.message}` 
        };
    }
}

/**
 * Build file tree structure
 */
async function buildFileTree(dirPath, relativePath = '') {
    const items = [];
    
    try {
        const entries = await fs.readdir(dirPath, { withFileTypes: true });
        
        for (const entry of entries) {
            const fullPath = path.join(dirPath, entry.name);
            const relPath = path.join(relativePath, entry.name).replace(/\\/g, '/');
            
            if (entry.isDirectory()) {
                const children = await buildFileTree(fullPath, relPath);
                items.push({
                    name: entry.name,
                    path: relPath,
                    type: 'directory',
                    children: children
                });
            } else {
                const stats = await fs.stat(fullPath);
                items.push({
                    name: entry.name,
                    path: relPath,
                    type: 'file',
                    size: stats.size
                });
            }
        }
    } catch (error) {
        console.error('❌ Error building file tree:', error);
    }
    
    return items.sort((a, b) => {
        // Directories first, then files
        if (a.type !== b.type) {
            return a.type === 'directory' ? -1 : 1;
        }
        return a.name.localeCompare(b.name);
    });
}

// Handle file reading
ipcMain.handle('read-file', async (event, filePath, projectPath) => {
    console.log('📖 Reading file:', filePath);
    
    try {
        const fullPath = path.join(projectPath, filePath);
        
        if (!await fs.pathExists(fullPath)) {
            throw new Error('الملف غير موجود');
        }

        const content = await fs.readFile(fullPath, 'utf8');
        
        return {
            success: true,
            content: content
        };

    } catch (error) {
        console.error('❌ Error reading file:', error);
        return { 
            success: false, 
            error: `خطأ في قراءة الملف: ${error.message}` 
        };
    }
});

// Handle file saving
ipcMain.handle('save-file', async (event, filePath, content, projectPath) => {
    console.log('💾 Saving file:', filePath);
    
    try {
        const fullPath = path.join(projectPath, filePath);
        
        // Ensure directory exists
        await fs.ensureDir(path.dirname(fullPath));
        
        // Write file
        await fs.writeFile(fullPath, content, 'utf8');
        
        console.log('✅ File saved successfully');
        
        return { success: true };

    } catch (error) {
        console.error('❌ Error saving file:', error);
        return { 
            success: false, 
            error: `خطأ في حفظ الملف: ${error.message}` 
        };
    }
});

// Test Chrome Web Store download functionality
ipcMain.handle('test-store-connection', async () => {
    console.log('🧪 Testing Chrome Web Store connection...');

    try {
        const testUrl = 'https://clients2.google.com/service/update2/crx?response=redirect&prodversion=120.0.6099.109&acceptformat=crx2,crx3&x=id%3Dcjpalhdlnbpafiamejdnhcphjbkeiagm%26uc';

        const response = await axios({
            method: 'HEAD', // Just check if URL is accessible
            url: testUrl,
            timeout: 10000,
            headers: {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
            }
        });

        console.log('✅ Store connection test successful:', response.status);
        return { success: true, status: response.status };

    } catch (error) {
        console.error('❌ Store connection test failed:', error.message);
        return { success: false, error: error.message };
    }
});

console.log('🎉 Main process initialized successfully');
